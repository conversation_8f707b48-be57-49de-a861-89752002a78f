'use client'

import { useState, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { createSecureBrowserClient } from '@/lib/session-security'
import { useOrgEntitySelection } from '@/hooks/useOrgEntitySelection'
import { useAuth } from '@/contexts/AuthContext'

const BFF_BASE_URL = process.env.NEXT_PUBLIC_BFF_URL || 'http://localhost:4000'

export default function InboxPage() {
  const router = useRouter()
  const supabase = useMemo(() => createSecureBrowserClient(), [])
  const { user } = useAuth()
  const { currentEntity, entities, isValid } = useOrgEntitySelection()

  const [showUpload, setShowUpload] = useState(false)
  const [file, setFile] = useState<File | null>(null)
  const [selectedEntityId, setSelectedEntityId] = useState<string | null>(
    currentEntity?.entity_id ? String(currentEntity.entity_id) : null
  )
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  const onOpenUpload = () => {
    setError(null)
    setSuccess(null)
    setFile(null)
    setSelectedEntityId(
      currentEntity?.entity_id ? String(currentEntity.entity_id) : null
    )
    setShowUpload(true)
  }

  const sanitizeName = (name: string) =>
    name.replace(/[^a-zA-Z0-9._-]/g, '-').toLowerCase()

  const handleUpload = async () => {
    try {
      setError(null)
      setSuccess(null)

      if (!user) {
        setError('You must be signed in to upload')
        return
      }
      if (!file) {
        setError('Please choose a file')
        return
      }
      const entityId = selectedEntityId
        ? Number(selectedEntityId)
        : (currentEntity?.entity_id ?? null)
      if (!entityId) {
        setError('Please select an entity')
        return
      }

      setUploading(true)

      const safeName = sanitizeName(file.name)
      const path = `entity-${entityId}/${Date.now()}-${safeName}`

      // 1) Upload file to Supabase Storage (bucket: inbox)
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('inbox')
        .upload(path, file, {
          cacheControl: '3600',
          upsert: false,
          contentType: file.type || 'application/octet-stream',
        })

      if (uploadError || !uploadData) {
        setError(uploadError?.message || 'Upload failed')
        setUploading(false)
        return
      }

      // 2) Notify BFF to create document and start processing
      const { data: sessionData } = await supabase.auth.getSession()
      const token = sessionData.session?.access_token

      const resp = await fetch(
        `${BFF_BASE_URL}/entities/${entityId}/documents`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...(token ? { Authorization: `Bearer ${token}` } : {}),
          },
          body: JSON.stringify({
            path: uploadData.path,
            mime_type: file.type || 'application/octet-stream',
            source: 'web-upload',
          }),
        }
      )

      if (!resp.ok) {
        const text = await resp.text()
        throw new Error(text || `BFF responded with ${resp.status}`)
      }

      setSuccess('Document uploaded. Processing has started.')
      setTimeout(() => {
        setShowUpload(false)
        // Optionally refresh the page or navigate; for now, stay on inbox
        router.refresh()
      }, 900)
    } catch (e) {
      setError(e instanceof Error ? e.message : 'Unexpected error')
    } finally {
      setUploading(false)
    }
  }
  return (
    <div className="max-w-6xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Inbox</h1>
        <p className="text-gray-600">
          Manage incoming documents and transactions
        </p>
      </div>

      {/* Filters and Actions */}
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex gap-2">
          <select className="px-3 py-2 border border-gray-300 rounded-md text-sm">
            <option>All Items</option>
            <option>Unprocessed</option>
            <option>Processed</option>
            <option>Requires Review</option>
          </select>
          <select className="px-3 py-2 border border-gray-300 rounded-md text-sm">
            <option>All Types</option>
            <option>Invoices</option>
            <option>Receipts</option>
            <option>Bank Statements</option>
          </select>
        </div>

        <div className="flex gap-2">
          <button
            onClick={onOpenUpload}
            className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 transition-colors"
            aria-label="Upload document"
          >
            Upload Document
          </button>
          <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md text-sm hover:bg-gray-50 transition-colors">
            Batch Process
          </button>
        </div>
      </div>

      {/* Inbox List */}
      <div className="bg-white rounded-lg shadow border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Document Queue</h2>
        </div>

        <div className="divide-y divide-gray-200">
          {/* Sample inbox items */}
          {[
            {
              id: 1,
              type: 'Invoice',
              description: 'Office Supplies - Invoice #INV-001',
              date: '2024-08-20',
              amount: '€234.50',
              status: 'Unprocessed',
              statusColor: 'bg-yellow-100 text-yellow-800',
            },
            {
              id: 2,
              type: 'Receipt',
              description: 'Business Lunch - Receipt',
              date: '2024-08-19',
              amount: '€45.20',
              status: 'Requires Review',
              statusColor: 'bg-orange-100 text-orange-800',
            },
            {
              id: 3,
              type: 'Bank Statement',
              description: 'Monthly Bank Statement - August 2024',
              date: '2024-08-18',
              amount: '€12,450.00',
              status: 'Processed',
              statusColor: 'bg-green-100 text-green-800',
            },
          ].map(item => (
            <div key={item.id} className="px-6 py-4 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-3">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-gray-600">
                          {item.type === 'Invoice'
                            ? '📄'
                            : item.type === 'Receipt'
                              ? '🧾'
                              : '🏦'}
                        </span>
                      </div>
                    </div>

                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">
                        {item.description}
                      </p>
                      <div className="flex items-center gap-4 mt-1">
                        <span className="text-xs text-gray-500">
                          {item.type}
                        </span>
                        <span className="text-xs text-gray-500">
                          {item.date}
                        </span>
                        <span className="text-xs font-medium text-gray-700">
                          {item.amount}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${item.statusColor}`}
                  >
                    {item.status}
                  </span>

                  <div className="flex gap-1">
                    <button className="p-1 text-gray-400 hover:text-gray-600">
                      <span className="sr-only">View</span>
                      👁️
                    </button>
                    <button className="p-1 text-gray-400 hover:text-gray-600">
                      <span className="sr-only">Edit</span>
                      ✏️
                    </button>
                    <button className="p-1 text-gray-400 hover:text-gray-600">
                      <span className="sr-only">Delete</span>
                      🗑️
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty state would go here when no items */}
        <div className="px-6 py-8 text-center text-gray-500 border-t border-gray-200">
          <p className="text-sm">Showing 3 of 3 items</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="mt-8 grid gap-6 md:grid-cols-3">
        <div className="bg-white rounded-lg shadow p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <span className="text-sm">⏳</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Pending</p>
              <p className="text-2xl font-bold text-gray-900">1</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                <span className="text-sm">🔍</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">
                Review Required
              </p>
              <p className="text-2xl font-bold text-gray-900">1</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-sm">✅</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Processed</p>
              <p className="text-2xl font-bold text-gray-900">1</p>
            </div>
          </div>
        </div>
      </div>

      {/* Upload Modal */}
      {showUpload && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
          <div className="bg-white w-full max-w-md rounded-lg shadow-xl border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900">
              Upload Document
            </h3>
            <p className="mt-1 text-sm text-gray-600">
              PDFs and images are supported.
            </p>

            {/* Entity selection when none is selected */}
            {(!isValid || !currentEntity) && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Select Entity
                </label>
                <select
                  value={selectedEntityId ?? ''}
                  onChange={e => setSelectedEntityId(e.target.value || null)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="">Choose an entity…</option>
                  {entities.map(e => (
                    <option key={e.entity_id} value={String(e.entity_id)}>
                      {e.entity_name}
                    </option>
                  ))}
                </select>
              </div>
            )}

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Choose File
              </label>
              <input
                type="file"
                accept="application/pdf,image/*"
                onChange={e => setFile(e.target.files?.[0] || null)}
                className="block w-full text-sm text-gray-700"
              />
            </div>

            {error && <p className="mt-3 text-sm text-red-600">{error}</p>}
            {success && (
              <p className="mt-3 text-sm text-green-600">{success}</p>
            )}

            <div className="mt-6 flex items-center justify-end gap-2">
              <button
                onClick={() => setShowUpload(false)}
                className="px-3 py-2 text-sm rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50"
                disabled={uploading}
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  void handleUpload()
                }}
                className="px-4 py-2 text-sm rounded-md bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-60"
                disabled={uploading}
              >
                {uploading ? 'Uploading…' : 'Upload'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
