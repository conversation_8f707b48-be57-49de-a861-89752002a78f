'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { getPendingInvitesForUser, listUserTenants } from '@ledgerly/dal'
import { useCSRF } from '@/hooks/useCSRF'

type PendingInvite = {
  token: string
  scope: 'tenant' | 'entity'
  scope_id: string
  email: string
  role: string
  expires_at: string
}

type TenantCreationStep = {
  name: string
  kind: 'SME' | 'FIRM'
}

type Tenant = {
  id: number
  name: string
  description: string | null
  created_by: string
  created_at: string
  role: string
}

export default function OnboardingPage() {
  const [loading, setLoading] = useState(true)
  const [pendingInvites, setPendingInvites] = useState<PendingInvite[]>([])
  const [currentStep, setCurrentStep] = useState<
    'checking' | 'invites' | 'create-tenant' | 'complete'
  >('checking')
  const [tenantForm, setTenantForm] = useState<TenantCreationStep>({
    name: '',
    kind: 'SME',
  })
  const [processing, setProcessing] = useState(false)
  const [error, setError] = useState('')

  const router = useRouter()
  const { user } = useAuth()
  const { /* csrfToken, */ addCSRFHeaders } = useCSRF()

  useEffect(() => {
    if (!user?.email) return

    const checkOnboardingStatus = async (): Promise<void> => {
      try {
        setLoading(true)

        // 1) Check for existing tenant memberships
        const tenants: Tenant[] =
          (await listUserTenants()) as unknown as Tenant[]
        if (tenants && tenants.length > 0) {
          setCurrentStep('complete')
          router.replace('/')
          return
        }

        // 2) Check for pending invites by user email
        const email = user.email as string
        const invites = await getPendingInvitesForUser(email)
        setPendingInvites(invites as unknown as PendingInvite[])
        setCurrentStep(invites.length > 0 ? 'invites' : 'create-tenant')
      } catch (err) {
        console.error('Onboarding check error:', err)
        setError(
          err instanceof Error
            ? err.message
            : 'Failed to check onboarding status'
        )
        setCurrentStep('create-tenant')
      } finally {
        setLoading(false)
      }
    }

    void checkOnboardingStatus()
  }, [user?.email, router])

  const handleAcceptInvite = (token: string): void => {
    setProcessing(true)
    setError('')

    const run = async () => {
      try {
        // Prefer our API route to include CSRF; DAL also works but we keep parity
        const resp = await fetch('/api/invites/accept', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...addCSRFHeaders(),
          },
          body: JSON.stringify({ token }),
        })
        const data = (await resp.json()) as {
          success?: boolean
          error?: string
        }
        if (!resp.ok || data.success !== true) {
          throw new Error(data.error || 'Failed to accept invitation')
        }
        // After accepting, go to dashboard; middleware may redirect if needed
        router.replace('/')
      } catch (err) {
        setError(
          err instanceof Error ? err.message : 'Failed to accept invitation'
        )
      } finally {
        setProcessing(false)
      }
    }

    void run()
  }

  const handleCreateTenant = (e: React.FormEvent): void => {
    e.preventDefault()
    setProcessing(true)
    setError('')

    try {
      if (!tenantForm.name.trim()) {
        throw new Error('Tenant name is required')
      }

      // await createTenantRPC(tenantForm.name.trim(), tenantForm.kind)
      throw new Error('Tenant creation temporarily disabled')

      // Success - redirect to dashboard
      router.replace('/')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create tenant')
    } finally {
      setProcessing(false)
    }
  }

  if (!user) {
    router.replace('/login')
    return null
  }

  if (loading || currentStep === 'checking') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Setting up your account...</p>
        </div>
      </div>
    )
  }

  if (currentStep === 'complete') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <p className="text-gray-600">Redirecting to dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-md mx-auto">
        <div className="bg-white rounded-lg shadow-md p-8">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-gray-900">
              Welcome to Ledgerly!
            </h1>
            <p className="text-gray-600 mt-2">Let&apos;s set up your account</p>
          </div>

          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}

          {currentStep === 'invites' && (
            <div className="space-y-6">
              <div>
                <h2 className="text-lg font-medium text-gray-900 mb-2">
                  You have pending invitations
                </h2>
                <p className="text-sm text-gray-600 mb-4">
                  Accept an invitation to join an existing organization, or skip
                  to create your own.
                </p>
                <div className="space-y-3">
                  {pendingInvites.map(invite => (
                    <div
                      key={invite.token}
                      className="border border-gray-200 rounded-md p-4"
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium text-gray-900">
                            {invite.scope === 'tenant'
                              ? 'Organization'
                              : 'Entity'}{' '}
                            Invitation
                          </p>
                          <p className="text-sm text-gray-600">
                            Role: {invite.role}
                          </p>
                          <p className="text-sm text-gray-500">
                            Expires:{' '}
                            {new Date(invite.expires_at).toLocaleDateString()}
                          </p>
                        </div>
                        <button
                          onClick={() => handleAcceptInvite(invite.token)}
                          disabled={processing}
                          className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 disabled:opacity-50"
                        >
                          Accept
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="text-center">
                <p className="text-sm text-gray-600 mb-3">
                  Don&apos;t see your invitation?
                </p>
                <button
                  onClick={() => setCurrentStep('create-tenant')}
                  className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                >
                  Create your own organization instead
                </button>
              </div>
            </div>
          )}

          {currentStep === 'create-tenant' && (
            <form onSubmit={handleCreateTenant} className="space-y-6">
              <div>
                <h2 className="text-lg font-medium text-gray-900 mb-2">
                  Create your organization
                </h2>
                <p className="text-sm text-gray-600 mb-4">
                  Set up your company in Ledgerly. You&apos;ll be the owner and
                  can invite team members later.
                </p>

                <div className="space-y-4">
                  <div>
                    <label
                      htmlFor="name"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Organization name
                    </label>
                    <input
                      id="name"
                      type="text"
                      required
                      value={tenantForm.name}
                      onChange={e =>
                        setTenantForm(prev => ({
                          ...prev,
                          name: e.target.value,
                        }))
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter your organization name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Organization type
                    </label>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="kind"
                          value="SME"
                          checked={tenantForm.kind === 'SME'}
                          onChange={e =>
                            setTenantForm(prev => ({
                              ...prev,
                              kind: e.target.value as 'SME' | 'FIRM',
                            }))
                          }
                          className="mr-3"
                        />
                        <div>
                          <span className="font-medium">
                            Small/Medium Enterprise (SME)
                          </span>
                          <p className="text-sm text-gray-600">
                            For individual businesses and small companies
                          </p>
                        </div>
                      </label>

                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="kind"
                          value="FIRM"
                          checked={tenantForm.kind === 'FIRM'}
                          onChange={e =>
                            setTenantForm(prev => ({
                              ...prev,
                              kind: e.target.value as 'SME' | 'FIRM',
                            }))
                          }
                          className="mr-3"
                        />
                        <div>
                          <span className="font-medium">Accounting Firm</span>
                          <p className="text-sm text-gray-600">
                            For accounting firms managing multiple clients
                          </p>
                        </div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <button
                type="submit"
                disabled={processing || !tenantForm.name.trim()}
                className="w-full py-2 px-4 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {processing ? 'Creating...' : 'Create Organization'}
              </button>
            </form>
          )}
        </div>
      </div>
    </div>
  )
}
