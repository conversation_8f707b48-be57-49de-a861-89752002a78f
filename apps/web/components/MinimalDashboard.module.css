/* MinimalDashboard CSS Module for hover effects and pseudo-elements */

.actionButton:hover {
  background: var(--bg) !important;
  border-color: var(--accent) !important;
}

.runButton:hover {
  background: var(--bg) !important;
}

.dismissButton:hover {
  background: rgba(146, 64, 14, 0.1) !important;
}

.userAvatar:focus,
.searchInput:focus,
.actionButton:focus,
.runButton:focus,
.dismissButton:focus {
  outline: 2px solid var(--accent-blue) !important;
  outline-offset: 2px !important;
}

.searchInput:focus {
  border-color: var(--accent-blue) !important;
}

.toggle {
  width: 32px;
  height: 18px;
  appearance: none;
  border-radius: 9px;
  position: relative;
  cursor: pointer;
  border: none;
  transition: background-color 0.2s ease;
  background: var(--border);
}

.toggle:checked {
  background: var(--accent-blue);
}

.toggle::before {
  content: '';
  position: absolute;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: white;
  top: 2px;
  left: 2px;
  transition: transform 0.2s ease;
}

.toggle:checked::before {
  transform: translateX(14px);
}

.toggle:focus {
  outline: 2px solid var(--accent-blue) !important;
  outline-offset: 2px !important;
}

/* Light theme variables */
.light {
  --bg: #F8FAFC;
  --text: #0B0F14;
  --text-subtle: #475569;
  --border: #E5E7EB;
  --accent: #111827;
  --accent-blue: #2563EB;
  --card-bg: #FFFFFF;
  --shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
}

/* Dark theme variables */
.dark {
  --bg: #0E1117;
  --text: #E5E7EB;
  --text-subtle: #9AA4B2;
  --border: #1F2937;
  --accent: #7AA2FF;
  --accent-blue: #7AA2FF;
  --card-bg: #1A1F2E;
  --shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Responsive adjustments */
@media (max-width: 1199px) {
  .agentsPanel {
    order: 2;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 12px 16px !important;
  }
  
  .mainContent {
    padding: 16px !important;
    gap: 16px !important;
  }
  
  .searchInput {
    width: 150px !important;
  }
  
  .brand {
    font-size: 18px !important;
  }
}
