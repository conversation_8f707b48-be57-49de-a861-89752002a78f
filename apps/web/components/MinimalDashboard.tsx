'use client'

import React, { useState, CSSProperties } from 'react'
import { useRouter } from 'next/navigation'
import styles from './MinimalDashboard.module.css'
import { useDashboardMetrics } from '@/hooks/useDashboardMetrics'
import { useVATSummary } from '@/hooks/useVATSummary'
import { useOrgEntitySelection } from '@/hooks/useOrgEntitySelection'
import { useAuth } from '@/contexts/AuthContext'

interface MinimalDashboardProps {
  className?: string
  'data-theme'?: 'light' | 'dark'
}

// Theme variables
const lightTheme = {
  bg: '#F8FAFC',
  text: '#0B0F14',
  textSubtle: '#475569',
  border: '#E5E7EB',
  accent: '#111827',
  accentBlue: '#2563EB',
  cardBg: '#FFFFFF',
  shadow: '0 1px 2px rgba(0, 0, 0, 0.08)',
}

const darkTheme = {
  bg: '#0E1117',
  text: '#E5E7EB',
  textSubtle: '#9AA4B2',
  border: '#1F2937',
  accent: '#7AA2FF',
  accentBlue: '#7AA2FF',
  cardBg: '#1A1F2E',
  shadow: '0 1px 2px rgba(0, 0, 0, 0.2)',
}

// Style functions
const getHeaderStyle = (theme: typeof lightTheme): CSSProperties => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: '16px 32px',
  borderBottom: `1px solid ${theme.border}`,
  background: theme.cardBg,
})

const getBrandStyle = (theme: typeof lightTheme): CSSProperties => ({
  fontSize: '20px',
  fontWeight: 600,
  margin: 0,
  color: theme.text,
})

const getSearchInputStyle = (theme: typeof lightTheme): CSSProperties => ({
  width: '320px',
  padding: '8px 16px',
  border: `1px solid ${theme.border}`,
  borderRadius: '12px',
  background: theme.bg,
  color: theme.text,
  fontSize: '14px',
})

const getUserAvatarStyle = (theme: typeof lightTheme): CSSProperties => ({
  width: '32px',
  height: '32px',
  borderRadius: '50%',
  background: theme.accent,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: 'white',
  fontSize: '14px',
  fontWeight: 500,
  cursor: 'pointer',
})

const getTrialBannerStyle = (dismissed: boolean): CSSProperties => ({
  display: dismissed ? 'none' : 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: '12px 32px',
  background: '#FEF3C7',
  borderBottom: '1px solid #F59E0B',
  color: '#92400E',
  fontSize: '14px',
})

const getDismissButtonStyle = (): CSSProperties => ({
  background: 'none',
  border: 'none',
  color: '#92400E',
  cursor: 'pointer',
  padding: '4px',
  borderRadius: '4px',
})

const getMainContentStyle = (): CSSProperties => ({
  display: 'grid',
  gridTemplateColumns: 'minmax(0, 1fr) 320px',
  gap: '32px',
  padding: '32px',
  maxWidth: '1400px',
  margin: '0 auto',
})

const getContentSectionStyle = (): CSSProperties => ({
  display: 'flex',
  flexDirection: 'column',
  gap: '32px',
})

const getMetricsGridStyle = (): CSSProperties => ({
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fit, minmax(260px, 1fr))',
  gap: '16px',
})

const getMetricCardStyle = (theme: typeof lightTheme): CSSProperties => ({
  background: theme.cardBg,
  border: `1px solid ${theme.border}`,
  borderRadius: '16px',
  padding: '20px',
  boxShadow: theme.shadow,
})

const getMetricLabelStyle = (theme: typeof lightTheme): CSSProperties => ({
  fontSize: '14px',
  fontWeight: 500,
  color: theme.textSubtle,
  margin: '0 0 8px 0',
})

const getMetricValueStyle = (theme: typeof lightTheme): CSSProperties => ({
  fontSize: '20px',
  fontWeight: 600,
  color: theme.text,
  margin: '0',
})

const getQuickActionsStyle = (): CSSProperties => ({
  display: 'flex',
  gap: '12px',
  flexWrap: 'wrap',
})

const getActionButtonStyle = (theme: typeof lightTheme): CSSProperties => ({
  padding: '12px 20px',
  border: `1px solid ${theme.border}`,
  borderRadius: '12px',
  background: theme.cardBg,
  color: theme.text,
  fontSize: '14px',
  fontWeight: 500,
  cursor: 'pointer',
  transition: 'all 0.2s ease',
})

const getDisabledActionButtonStyle = (
  theme: typeof lightTheme
): CSSProperties => ({
  ...getActionButtonStyle(theme),
  opacity: 0.6,
  cursor: 'not-allowed',
  background: theme.cardBg,
  color: theme.textSubtle,
})

const getAgentsPanelStyle = (theme: typeof lightTheme): CSSProperties => ({
  background: theme.cardBg,
  border: `1px solid ${theme.border}`,
  borderRadius: '16px',
  padding: '24px',
  boxShadow: theme.shadow,
  height: 'fit-content',
})

const getAgentsPanelTitleStyle = (theme: typeof lightTheme): CSSProperties => ({
  fontSize: '20px',
  fontWeight: 600,
  margin: '0 0 20px 0',
  color: theme.text,
})

const getAgentCardStyle = (
  theme: typeof lightTheme,
  isLast: boolean
): CSSProperties => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: '12px 0',
  borderBottom: isLast ? 'none' : `1px solid ${theme.border}`,
})

const getAgentInfoStyle = (): CSSProperties => ({
  flex: 1,
})

const getAgentNameStyle = (theme: typeof lightTheme): CSSProperties => ({
  fontSize: '14px',
  fontWeight: 500,
  margin: '0 0 4px 0',
  color: theme.text,
})

const getAgentPurposeStyle = (theme: typeof lightTheme): CSSProperties => ({
  fontSize: '12px',
  color: theme.textSubtle,
  margin: '0',
})

const getAgentControlsStyle = (): CSSProperties => ({
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
})

const getStatusPillStyle = (
  status: 'idle' | 'running' | 'done'
): CSSProperties => {
  const baseStyle: CSSProperties = {
    padding: '4px 8px',
    borderRadius: '12px',
    fontSize: '12px',
    fontWeight: 500,
  }

  switch (status) {
    case 'idle':
      return { ...baseStyle, background: '#F1F5F9', color: '#0B0F14' }
    case 'running':
      return { ...baseStyle, background: '#D6E4FF', color: '#1D4ED8' }
    case 'done':
      return { ...baseStyle, background: '#DCFCE7', color: '#166534' }
  }
}

const getRunButtonStyle = (theme: typeof lightTheme): CSSProperties => ({
  padding: '6px 12px',
  border: `1px solid ${theme.border}`,
  borderRadius: '8px',
  background: theme.cardBg,
  color: theme.text,
  fontSize: '12px',
  cursor: 'pointer',
})

const getToggleStyle = (): CSSProperties => ({
  // Most styles are handled by CSS module for pseudo-selectors
})

const getRecentActivityCardStyle = (
  theme: typeof lightTheme
): CSSProperties => ({
  background: theme.cardBg,
  border: `1px solid ${theme.border}`,
  borderRadius: '16px',
  padding: '24px',
  boxShadow: theme.shadow,
})

const getSectionTitleStyle = (theme: typeof lightTheme): CSSProperties => ({
  fontSize: '20px',
  fontWeight: 600,
  margin: '0 0 16px 0',
  color: theme.text,
})

const getEmptyStateStyle = (theme: typeof lightTheme): CSSProperties => ({
  textAlign: 'center',
  padding: '32px',
  color: theme.textSubtle,
})

const getEmptyStateTextStyle = (): CSSProperties => ({
  fontSize: '16px',
  margin: '0 0 8px 0',
})

const getEmptyStateSubtextStyle = (): CSSProperties => ({
  fontSize: '14px',
  margin: '0',
})

export default function MinimalDashboard({
  className,
  'data-theme': themeMode = 'light',
}: MinimalDashboardProps) {
  const [bannerDismissed, setBannerDismissed] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [navigating, setNavigating] = useState<string | null>(null)
  const theme = themeMode === 'dark' ? darkTheme : lightTheme
  const router = useRouter()
  const { user } = useAuth()

  // Data hooks
  const {
    metrics,
    loading: metricsLoading,
    error: metricsError,
    refreshMetrics,
  } = useDashboardMetrics()
  const {
    vatPosition,
    loading: vatLoading,
    error: vatError,
    refreshVAT,
  } = useVATSummary()
  const {
    currentEntity,
    isValid,
    loading: entityLoading,
  } = useOrgEntitySelection()

  // Debug entity context (can be removed in production)
  React.useEffect(() => {
    console.log('Dashboard state:', {
      user: user ? 'authenticated' : 'not authenticated',
      isValid,
      currentEntity: currentEntity ? currentEntity.entity_name : 'none',
      entityLoading,
      navigating,
    })
  }, [user, isValid, currentEntity, entityLoading, navigating])

  // Redirect to login if not authenticated
  React.useEffect(() => {
    if (!user && !entityLoading) {
      console.log('User not authenticated, redirecting to login...')
      router.push('/login')
    }
  }, [user, entityLoading, router])

  React.useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 1200)
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Clear navigation state after a delay to handle route transitions
  React.useEffect(() => {
    if (navigating) {
      const timer = setTimeout(() => {
        setNavigating(null)
      }, 2000) // Clear after 2 seconds
      return () => clearTimeout(timer)
    }
  }, [navigating])

  const dashboardStyle: CSSProperties = {
    minHeight: '100vh',
    background: theme.bg,
    color: theme.text,
    fontFamily:
      "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif",
    lineHeight: 1.4,
  }

  const responsiveMainContentStyle: CSSProperties = {
    ...getMainContentStyle(),
    gridTemplateColumns: isMobile ? '1fr' : 'minmax(0, 1fr) 320px',
    gap: isMobile ? '24px' : '32px',
  }

  const responsiveSearchStyle: CSSProperties = {
    ...getSearchInputStyle(theme),
    width: isMobile ? '200px' : '320px',
  }

  // Format metrics for display
  const formatCurrency = (amount: number, currency = 'EUR') => {
    return new Intl.NumberFormat('en-BE', {
      style: 'currency',
      currency,
    }).format(amount)
  }

  const formatVATPosition = () => {
    if (vatLoading) return 'Loading...'
    if (vatError) return 'Error loading VAT'
    if (!vatPosition) return 'VAT not configured'
    const amount = formatCurrency(vatPosition.amount, vatPosition.currency)
    return `${amount} ${vatPosition.type}`
  }

  const formatBankBalance = () => {
    if (metricsLoading) return 'Loading...'
    if (metricsError) return 'Error loading balance'
    if (!metrics.bankBalance) return 'No accounts'
    return formatCurrency(
      metrics.bankBalance.total,
      metrics.bankBalance.currency
    )
  }

  const formatOpenInvoices = () => {
    if (metricsLoading) return 'Loading...'
    if (metricsError) return 'Error loading invoices'
    if (!metrics.openInvoices) return 'No invoices'
    const amount = formatCurrency(
      metrics.openInvoices.totalAmount,
      metrics.openInvoices.currency
    )
    return `${metrics.openInvoices.count} (${amount})`
  }

  const formatInboxCount = () => {
    if (metricsLoading) return 'Loading...'
    if (metricsError) return 'Error loading inbox'
    if (!metrics.inboxToReview) return 'No documents'
    const count = metrics.inboxToReview.count
    return `${count} document${count !== 1 ? 's' : ''}`
  }

  const formatReconciliationTasks = () => {
    if (metricsLoading) return 'Loading...'
    if (metricsError) return 'Error loading tasks'
    if (!metrics.reconciliationTasks) return 'No tasks'
    const count = metrics.reconciliationTasks.count
    return `${count} pending`
  }

  const dashboardMetrics = [
    { label: 'Next VAT', value: formatVATPosition() },
    { label: 'Bank Balance', value: formatBankBalance() },
    { label: 'Open Invoices', value: formatOpenInvoices() },
    { label: 'Inbox to Review', value: formatInboxCount() },
    { label: 'Reconciliation Tasks', value: formatReconciliationTasks() },
  ]

  const agents = [
    {
      name: 'Invoice Extractor',
      purpose: 'Extracts data from uploaded invoices',
      status: 'idle' as const,
    },
    {
      name: 'Bank Reconciler',
      purpose: 'Matches transactions automatically',
      status: 'running' as const,
    },
    {
      name: 'VAT Co-Pilot',
      purpose: 'Assists with VAT calculations',
      status: 'done' as const,
    },
    {
      name: 'Compliance Reminders',
      purpose: 'Tracks deadlines and requirements',
      status: 'idle' as const,
    },
  ]

  const handleRefreshData = async () => {
    await Promise.all([refreshMetrics(), refreshVAT()])
  }

  // Show entity context in header
  const getEntityDisplayName = () => {
    if (!user) return 'Not Authenticated'
    if (!isValid || !currentEntity) return 'No Entity Selected'
    return currentEntity.entity_name
  }

  // Show loading state if user is not authenticated
  if (!user) {
    return (
      <div
        style={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: theme.bg,
          color: theme.text,
          fontFamily:
            "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif",
        }}
      >
        <div style={{ textAlign: 'center' }}>
          <p>Redirecting to login...</p>
        </div>
      </div>
    )
  }

  // Navigation handlers for action buttons
  const handleNavigateToInbox = () => {
    console.log('Upload Document button clicked!', {
      isValid,
      currentEntity,
      navigating,
    })

    // Always allow navigation. The Inbox page will guide entity selection and upload.
    try {
      console.log('Starting navigation to inbox...')
      setNavigating('inbox')
      router.push('/inbox')
    } catch (error) {
      console.error('Navigation to inbox failed:', error)
      setNavigating(null)
    }
  }

  const handleNavigateToInvoiceCreation = () => {
    console.log('Create Invoice button clicked!', {
      isValid,
      currentEntity,
      navigating,
    })

    // Allow navigation even if entity isn't selected; the ledger will handle context.
    try {
      console.log('Starting navigation to ledger (invoice creation)...')
      setNavigating('invoice')
      // Navigate to ledger as invoice creation UI doesn't exist yet
      router.push('/ledger')
    } catch (error) {
      console.error('Navigation to invoice creation failed:', error)
      setNavigating(null)
    }
  }

  const handleNavigateToBankReconciliation = () => {
    console.log('Reconcile Bank button clicked!', {
      isValid,
      currentEntity,
      navigating,
    })

    // Allow navigation even if entity isn't selected; the ledger will handle context.
    try {
      console.log('Starting navigation to ledger (bank reconciliation)...')
      setNavigating('reconcile')
      // Navigate to ledger as bank reconciliation UI doesn't exist yet
      router.push('/ledger')
    } catch (error) {
      console.error('Navigation to bank reconciliation failed:', error)
      setNavigating(null)
    }
  }

  return (
    <div
      className={`${className} ${themeMode === 'dark' ? styles.dark : styles.light}`}
      style={dashboardStyle}
    >
      <header className={styles.header} style={getHeaderStyle(theme)}>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
          <h1 className={styles.brand} style={getBrandStyle(theme)}>
            BelBooks
          </h1>
          <p style={{ fontSize: '12px', color: theme.textSubtle, margin: 0 }}>
            {getEntityDisplayName()}
          </p>
        </div>
        <input
          className={styles.searchInput}
          style={responsiveSearchStyle}
          placeholder="Search transactions, invoices, contacts..."
        />
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <button
            onClick={() => {
              void handleRefreshData()
            }}
            style={{
              background: 'none',
              border: `1px solid ${theme.border}`,
              borderRadius: '8px',
              padding: '6px 12px',
              color: theme.text,
              fontSize: '12px',
              cursor: 'pointer',
            }}
            aria-label="Refresh data"
          >
            ↻
          </button>
          <div
            className={styles.userAvatar}
            style={getUserAvatarStyle(theme)}
            tabIndex={0}
            role="button"
            aria-label="User menu"
          >
            JK
          </div>
        </div>
      </header>

      <div style={getTrialBannerStyle(bannerDismissed)}>
        <span>
          Trial expires in 14 days - Upgrade to continue using BelBooks
        </span>
        <button
          className={styles.dismissButton}
          style={getDismissButtonStyle()}
          onClick={() => setBannerDismissed(true)}
          aria-label="Dismiss banner"
        >
          ×
        </button>
      </div>

      <main className={styles.mainContent} style={responsiveMainContentStyle}>
        <section style={getContentSectionStyle()}>
          <div style={getMetricsGridStyle()}>
            {dashboardMetrics.map((metric, index) => (
              <div key={index} style={getMetricCardStyle(theme)}>
                <p style={getMetricLabelStyle(theme)}>{metric.label}</p>
                <p style={getMetricValueStyle(theme)}>{metric.value}</p>
              </div>
            ))}
          </div>

          <div style={getQuickActionsStyle()}>
            <button
              className={styles.actionButton}
              style={
                navigating === 'inbox'
                  ? getDisabledActionButtonStyle(theme)
                  : getActionButtonStyle(theme)
              }
              onClick={handleNavigateToInbox}
              disabled={navigating === 'inbox'}
              aria-label="Upload Document - Navigate to Inbox"
              title={'Upload and manage documents in the inbox'}
            >
              {navigating === 'inbox' ? 'Loading...' : 'Upload Document'}
            </button>
            <button
              className={styles.actionButton}
              style={
                navigating === 'invoice'
                  ? getDisabledActionButtonStyle(theme)
                  : getActionButtonStyle(theme)
              }
              onClick={handleNavigateToInvoiceCreation}
              disabled={navigating === 'invoice'}
              aria-label="Create Invoice - Navigate to Ledger"
              title={'Create and manage invoices (opens ledger)'}
            >
              {navigating === 'invoice' ? 'Loading...' : 'Create Invoice'}
            </button>
            <button
              className={styles.actionButton}
              style={
                navigating === 'reconcile'
                  ? getDisabledActionButtonStyle(theme)
                  : getActionButtonStyle(theme)
              }
              onClick={handleNavigateToBankReconciliation}
              disabled={navigating === 'reconcile'}
              aria-label="Reconcile Bank - Navigate to Ledger"
              title={'Reconcile bank transactions (opens ledger)'}
            >
              {navigating === 'reconcile' ? 'Loading...' : 'Reconcile Bank'}
            </button>
          </div>

          <div style={getRecentActivityCardStyle(theme)}>
            <h2 style={getSectionTitleStyle(theme)}>Recent Activity</h2>
            <div style={getEmptyStateStyle(theme)}>
              <p style={getEmptyStateTextStyle()}>
                No recent activity to display
              </p>
              <p style={getEmptyStateSubtextStyle()}>
                Activity will appear here once you start using the application
              </p>
            </div>
          </div>
        </section>

        <aside
          className={styles.agentsPanel}
          style={getAgentsPanelStyle(theme)}
        >
          <h2 style={getAgentsPanelTitleStyle(theme)}>Agents</h2>
          {agents.map((agent, index) => (
            <div
              key={index}
              style={getAgentCardStyle(theme, index === agents.length - 1)}
            >
              <div style={getAgentInfoStyle()}>
                <h3 style={getAgentNameStyle(theme)}>{agent.name}</h3>
                <p style={getAgentPurposeStyle(theme)}>{agent.purpose}</p>
              </div>
              <div style={getAgentControlsStyle()}>
                <span style={getStatusPillStyle(agent.status)}>
                  {agent.status.charAt(0).toUpperCase() + agent.status.slice(1)}
                </span>
                <button
                  className={styles.runButton}
                  style={getRunButtonStyle(theme)}
                >
                  Run
                </button>
                <input
                  type="checkbox"
                  defaultChecked
                  aria-label={`Toggle ${agent.name}`}
                  className={styles.toggle}
                  style={getToggleStyle()}
                />
              </div>
            </div>
          ))}
        </aside>
      </main>
    </div>
  )
}
