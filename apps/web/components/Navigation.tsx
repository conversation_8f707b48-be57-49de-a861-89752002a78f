'use client'

import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { SessionTimeoutIndicator } from '@/components/SessionTimeoutDialog'
import { OrgEntityPicker } from '@/components/OrgEntityPicker'

const navigationItems = [
  { name: 'Dashboard', href: '/' as const, icon: '📊' },
  { name: 'Inbox', href: '/inbox' as const, icon: '📥' },
  { name: 'Ledger', href: '/ledger' as const, icon: '📚' },
  { name: 'VAT', href: '/vat' as const, icon: '📄' },
  { name: 'Roles', href: '/roles' as const, icon: '👥' },
]

export function Navigation() {
  const pathname = usePathname()
  const router = useRouter()
  const { user, loading, signOut, sessionSecurity } = useAuth()

  // Don't show navigation on auth pages
  if (
    pathname === '/login' ||
    pathname === '/forgot-password' ||
    pathname === '/reset-password' ||
    pathname.startsWith('/auth/') ||
    pathname === '/onboarding'
  ) {
    return null
  }

  // Show loading state
  if (loading) {
    return (
      <nav className="w-64 bg-gray-50 border-r border-gray-200 p-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-2"></div>
          <div className="h-4 bg-gray-200 rounded mb-8"></div>
          <div className="space-y-2">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-10 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </nav>
    )
  }

  // Redirect to login if not authenticated
  if (!user) {
    router.replace('/login')
    return null
  }

  const handleSignOut = async () => {
    try {
      await signOut()
      router.replace('/login')
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }

  return (
    <nav className="w-64 bg-gray-50 border-r border-gray-200 p-4 flex flex-col h-full">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Ledgerly</h1>
        <p className="text-sm text-gray-600">Accounting Management</p>
      </div>

      {/* Organization/Entity Picker */}
      <div className="mb-6">
        <OrgEntityPicker />
      </div>

      <div className="space-y-2 flex-1">
        {navigationItems.map(item => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.href}
              href={item.href}
              className={`flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                isActive
                  ? 'bg-blue-100 text-blue-900 border-blue-200 border'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
              }`}
            >
              <span className="text-lg">{item.icon}</span>
              <span>{item.name}</span>
            </Link>
          )
        })}
      </div>

      {/* Session timeout indicator */}
      {sessionSecurity.timeoutInfo && sessionSecurity.isActive && (
        <div className="mb-4">
          <SessionTimeoutIndicator
            timeRemaining={sessionSecurity.timeoutInfo.timeUntilTimeout}
            isWarning={sessionSecurity.showWarning}
            onClick={() => void sessionSecurity.extendSession()}
          />
        </div>
      )}

      {/* User section */}
      <div className="mt-auto pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600 truncate">{user.email}</div>
          <button
            onClick={() => void handleSignOut()}
            className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
            title="Sign out"
          >
            🚪
          </button>
        </div>
      </div>
    </nav>
  )
}
