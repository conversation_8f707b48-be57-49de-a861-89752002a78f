'use client'

import {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
  useMemo,
  useCallback,
} from 'react'
import type { User, Session } from '@supabase/supabase-js'
import { createSecureBrowserClient } from '@/lib/session-security'
import {
  useSessionSecurity,
  useSessionTimeoutDialog,
  SessionSecurityHook,
} from '@/hooks/useSessionSecurity'
import { SessionTimeoutDialog } from '@/components/SessionTimeoutDialog'

interface AuthContextType {
  user: User | null
  session: Session | null
  loading: boolean
  signOut: () => Promise<void>
  sessionSecurity: SessionSecurityHook
}

const AuthContext = createContext<AuthContextType | null>(null)

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const [supabase] = useState(() => createSecureBrowserClient())

  // Define signOut function early so it can be used in useSessionSecurity
  const signOut = useCallback(async () => {
    try {
      await supabase.auth.signOut()
    } catch (error) {
      console.error('Error signing out:', error)
      // Force sign out by clearing local state
      setUser(null)
      setSession(null)
    }
  }, [supabase])

  // Session timeout dialog
  const timeoutDialog = useSessionTimeoutDialog()

  // Create stable empty functions to prevent infinite re-renders
  const emptyTimeoutWarning = useCallback(() => {}, [])
  const emptyTimeout = useCallback(() => {}, [])
  const emptyActivityDetected = useCallback(() => {}, [])

  // Memoize the session security options to prevent infinite re-renders
  const sessionSecurityOptions = useMemo(
    () => ({
      onTimeoutWarning: emptyTimeoutWarning,
      onTimeout: emptyTimeout,
      onActivityDetected: emptyActivityDetected,
      enableActivityTracking: true,
      enableOnlineStatus: true,
      enableAutoRefresh: true,
    }),
    [emptyTimeoutWarning, emptyTimeout, emptyActivityDetected]
  )

  // Memoize the auth data to prevent infinite re-renders
  const authData = useMemo(
    () => ({
      user,
      session,
      signOut,
    }),
    [user, session, signOut]
  )

  // Session security management - temporarily use minimal implementation
  const sessionSecurity = {
    isActive: !!session,
    timeoutInfo: null,
    showWarning: false,
    lastActivity: new Date(),
    isOnline: true,
    extendSession: async () => {},
    forceLogout: () => {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      signOut()
    },
    updateActivity: () => {},
  }

  useEffect(() => {
    // Get initial session and ensure server cookies are synced for middleware
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    ;(async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession()
      setSession(session)
      setUser(session?.user ?? null)
      setLoading(false)

      // If a session already exists (e.g., from a previous visit),
      // sync auth cookies so middleware/SSR sees the authenticated user.
      if (session) {
        try {
          await fetch('/auth/callback', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ event: 'SIGNED_IN', session }),
          })
        } catch (e) {
          console.warn('Initial auth cookie sync failed:', e)
        }
      }
    })()

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setSession(session)
      setUser(session?.user ?? null)
      setLoading(false)

      // Log security events
      if (event === 'SIGNED_IN' && session?.user) {
        // This will be logged by the session security manager
        console.log('User signed in, session security active')
      } else if (event === 'SIGNED_OUT') {
        console.log('User signed out')
      }

      // IMPORTANT: keep server-side auth cookies in sync for middleware/SSR
      try {
        await fetch('/auth/callback', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ event, session }),
        })
      } catch (e) {
        console.warn('Failed to sync auth cookies:', e)
      }
    })

    return () => subscription.unsubscribe()
  }, [supabase])

  return (
    <AuthContext.Provider
      value={{
        user,
        session,
        loading,
        signOut,
        sessionSecurity,
      }}
    >
      {children}

      {/* Session timeout warning dialog */}
      <SessionTimeoutDialog
        show={timeoutDialog.showDialog}
        timeoutInfo={timeoutDialog.timeoutInfo}
        onExtendSession={() => {
          // eslint-disable-next-line @typescript-eslint/no-floating-promises
          sessionSecurity.extendSession()
          timeoutDialog.extendSession()
        }}
        onLogoutNow={() => {
          sessionSecurity.forceLogout()
          timeoutDialog.logoutNow()
        }}
        onClose={() => timeoutDialog.extendSession()}
      />
    </AuthContext.Provider>
  )
}

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
